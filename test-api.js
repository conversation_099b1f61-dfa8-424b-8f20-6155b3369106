// 测试API请求
const axios = require('axios');

async function testAPI() {
  console.log('开始测试API请求...');
  
  try {
    console.log('发送POST请求到: http://**************:8080/api/pos/getPosStore');
    
    const response = await axios.post(
      'http://localhost:8081/api/v1/stores/sync',
      {}, // 空的请求体
      {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000 // 10秒超时
      }
    );
    
    console.log('请求成功!');
    console.log('状态码:', response.status);
    console.log('响应头:', response.headers);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data && response.data.rsCode === '00000000') {
      console.log('✅ API返回成功');
      console.log('门店数量:', response.data.body.storeInfoList.length);
    } else {
      console.log('❌ API返回错误:', response.data);
    }
    
  } catch (error) {
    console.error('❌ 请求失败:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      console.error('请求配置:', error.config);
      console.error('没有收到响应');
    } else {
      console.error('请求设置错误:', error.message);
    }
  }
}

testAPI();
