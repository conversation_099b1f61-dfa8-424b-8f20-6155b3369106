import { defineStore } from "pinia";
import { ref } from "vue";
import {
  loginWithCode,
  sendVerificationCode as sendCode<PERSON><PERSON>,
  getUserInfo as getUserInfoApi,
} from "@/api";

export const useUserStore = defineStore("user", () => {
  // 用户信息
  const userInfo = ref(null);
  const token = ref(localStorage.getItem("token") || "");
  const isLoggedIn = ref(!!token.value);

  // 登录
  const login = async (phone, code) => {
    try {
      // 优先使用真实API，失败时使用模拟数据
      let response;
      try {
        response = await loginWithCode(phone, code);
      } catch (apiError) {
        console.warn("API登录失败，使用模拟登录:", apiError.message);

        // 模拟登录成功（开发环境或API不可用时）
        if (code === "123456") {
          response = {
            token: "mock_token_" + Date.now(),
            user: {
              id: "1001",
              phone: phone,
              name: "用户" + phone.slice(-4),
              avatar: "",
              role: "admin",
              permissions: ["dashboard", "pos-usage", "order-analysis"],
            },
          };
        } else {
          throw new Error("验证码错误");
        }
      }

      // 保存用户信息
      userInfo.value = response.user;
      token.value = response.token;
      isLoggedIn.value = true;

      // 持久化存储
      localStorage.setItem("token", response.token);
      localStorage.setItem("userInfo", JSON.stringify(response.user));

      return { success: true, data: response.user };
    } catch (error) {
      console.error("登录失败:", error);
      return { success: false, message: error.message || "登录失败" };
    }
  };

  // 登出
  const logout = () => {
    userInfo.value = null;
    token.value = "";
    isLoggedIn.value = false;

    // 清除本地存储
    localStorage.removeItem("token");
    localStorage.removeItem("userInfo");
  };

  // 初始化用户信息（从本地存储恢复）
  const initUserInfo = () => {
    const storedUserInfo = localStorage.getItem("userInfo");
    if (storedUserInfo && token.value) {
      try {
        userInfo.value = JSON.parse(storedUserInfo);
        isLoggedIn.value = true;
      } catch (error) {
        console.error("解析用户信息失败:", error);
        logout();
      }
    }
  };

  // 发送验证码
  const sendVerificationCode = async (phone) => {
    try {
      // 优先使用真实API
      try {
        await sendCodeApi(phone);
        console.log(`验证码已发送到 ${phone}`);
        return { success: true };
      } catch (apiError) {
        console.warn("API发送验证码失败，使用模拟模式:", apiError.message);

        // 模拟发送成功
        console.log(`验证码已发送到 ${phone}`);

        // 开发环境下显示模拟验证码
        if (process.env.NODE_ENV === "development") {
          const mockCode = "123456";
          console.log(`🔐 开发环境模拟验证码: ${mockCode}`);
          return { success: true, code: mockCode };
        }

        return { success: true };
      }
    } catch (error) {
      console.error("发送验证码失败:", error);
      return { success: false, message: error.message || "发送验证码失败" };
    }
  };

  return {
    // 状态
    userInfo,
    token,
    isLoggedIn,

    // 方法
    login,
    logout,
    initUserInfo,
    sendVerificationCode,
  };
});
