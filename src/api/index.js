import axios from 'axios'
import mockData from '../mock/data'

// 在实际项目中，这里会配置真实的API请求
// 这里我们使用模拟数据

export const getDashboardData = () => {
  return Promise.resolve(mockData.dashboardData)
}

export const getPosUsageData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { storeId, date, type } = params || {}
  const { storeId } = params || {}
  // 模拟数据过滤
  let data = mockData.posUsageData
  if (storeId) {
    data = data.filter(item => item.storeId === storeId)
  }
  return Promise.resolve(data)
}

export const getOrderAnalysisData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { date, storeId, type } = params || {}
  return Promise.resolve(mockData.orderAnalysisData)
}

export const getDeviceStatusData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.deviceStatusData)
}

export const getEnergyOptimizationData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.energyOptimizationData)
}

export const getStoreList = () => {
  return Promise.resolve(mockData.storeList)
}

export const getEnergyConsumption = (params) => {
  const { storeId, date } = params || {}
  // 实际项目中这些参数将用于过滤数据
  return Promise.resolve(mockData.energyConsumptionData)
} 