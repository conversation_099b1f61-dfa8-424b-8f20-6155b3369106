import axios from 'axios'
import mockData from '../mock/data'

// 在实际项目中，这里会配置真实的API请求
// 这里我们使用模拟数据

export const getDashboardData = () => {
  return Promise.resolve(mockData.dashboardData)
}

export const getPosUsageData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { storeId, date, type } = params || {}
  const { storeId } = params || {}
  // 模拟数据过滤
  let data = mockData.posUsageData
  if (storeId) {
    data = data.filter(item => item.storeId === storeId)
  }
  return Promise.resolve(data)
}

export const getOrderAnalysisData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { date, storeId, type } = params || {}
  return Promise.resolve(mockData.orderAnalysisData)
}

export const getDeviceStatusData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.deviceStatusData)
}

export const getEnergyOptimizationData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.energyOptimizationData)
}

export const getStoreList = () => {
  return Promise.resolve(mockData.storeList)
}

export const getEnergyConsumption = (params) => {
  const { storeId, date } = params || {}
  // 实际项目中这些参数将用于过滤数据
  return Promise.resolve(mockData.energyConsumptionData)
}

// 获取门店数据的API接口
export const getPosStore = async () => {
  try {
    console.log('开始请求门店数据...')

    const response = await axios.post(
      'http://localhost:8081/api/v1/stores/sync',
      {}, // POST请求体（空对象，因为入参是空）
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('API响应:', response.data)

    if (response.data && response.data.rsCode === '00000000') {
      console.log('门店数据获取成功:', response.data.body.storeInfoList)
      return response.data.body.storeInfoList
    } else {
      console.error('API返回错误:', response.data)
      throw new Error(response.data?.msg || '获取门店数据失败')
    }
  } catch (error) {
    console.error('获取门店数据失败:', error)
    console.log('使用备用数据')

    // 返回模拟数据作为备用
    return [
      {
        "storeId": "5094",
        "storeName": " 长安霄边店",
        "pgSeq": "5",
        "pgName": "华南地区",
        "subId": "5_11",
        "subArea": "粤东区"
      },
      {
        "storeId": "5096",
        "storeName": "海源店",
        "pgSeq": "5",
        "pgName": "华南地区",
        "subId": "5_15",
        "subArea": "云贵区"
      }
    ]
  }
}