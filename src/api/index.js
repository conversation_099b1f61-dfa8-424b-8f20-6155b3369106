import axios from 'axios'
import mockData from '../mock/data'

// 在实际项目中，这里会配置真实的API请求
// 这里我们使用模拟数据

export const getDashboardData = () => {
  return Promise.resolve(mockData.dashboardData)
}

export const getPosUsageData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { storeId, date, type } = params || {}
  const { storeId } = params || {}
  // 模拟数据过滤
  let data = mockData.posUsageData
  if (storeId) {
    data = data.filter(item => item.storeId === storeId)
  }
  return Promise.resolve(data)
}

export const getOrderAnalysisData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { date, storeId, type } = params || {}
  return Promise.resolve(mockData.orderAnalysisData)
}

export const getDeviceStatusData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.deviceStatusData)
}

export const getEnergyOptimizationData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.energyOptimizationData)
}

export const getStoreList = () => {
  return Promise.resolve(mockData.storeList)
}

export const getEnergyConsumption = (params) => {
  const { storeId, date } = params || {}
  // 实际项目中这些参数将用于过滤数据
  return Promise.resolve(mockData.energyConsumptionData)
}

// 获取门店数据的API接口
export const getPosStore = async () => {
  try {
    const response = await axios.get('http://10.211.241.215:8080/api/pos/getPosStore', {
      headers: {
        'Content-Type': 'application/json'
      }
    })

    if (response.data && response.data.rsCode === '00000000') {
      return response.data.body.storeInfoList
    } else {
      throw new Error(response.data?.msg || '获取门店数据失败')
    }
  } catch (error) {
    console.error('获取门店数据失败:', error)
    // 返回模拟数据作为备用
    return [
      {
        "storeId": "5094",
        "storeName": " 长安霄边店",
        "pgSeq": "5",
        "pgName": "华南地区",
        "subId": "5_11",
        "subArea": "粤东区"
      },
      {
        "storeId": "5096",
        "storeName": "海源店",
        "pgSeq": "5",
        "pgName": "华南地区",
        "subId": "5_15",
        "subArea": "云贵区"
      }
    ]
  }
}