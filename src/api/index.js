/* eslint-disable no-unused-vars */
import request from '@/utils/request'
import config, { API_ENDPOINTS } from '@/config/api'
import mockData from '../mock/data'

// 在实际项目中，这里会配置真实的API请求
// 这里我们使用模拟数据

export const getDashboardData = () => {
  return Promise.resolve(mockData.dashboardData)
}

export const getPosUsageData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { storeId, date, type } = params || {}
  const { storeId } = params || {}
  // 模拟数据过滤
  let data = mockData.posUsageData
  if (storeId) {
    data = data.filter(item => item.storeId === storeId)
  }
  return Promise.resolve(data)
}

export const getOrderAnalysisData = (params) => {
  // 实际项目中这些参数将用于过滤数据
  // const { date, storeId, type } = params || {}
  return Promise.resolve(mockData.orderAnalysisData)
}

export const getDeviceStatusData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.deviceStatusData)
}

export const getEnergyOptimizationData = (params) => {
  const { storeId } = params || {}
  return Promise.resolve(mockData.energyOptimizationData)
}

export const getStoreList = () => {
  return Promise.resolve(mockData.storeList)
}

export const getEnergyConsumption = (params) => {
  const { storeId, date } = params || {}
  // 实际项目中这些参数将用于过滤数据
  return Promise.resolve(mockData.energyConsumptionData)
}

// 获取门店数据的API接口
export const getPosStore = async () => {
  try {
    console.log('开始请求门店数据...')
    console.log('API配置:', config)
    console.log('请求URL:', `${config.apiPrefix}${API_ENDPOINTS.POS_STORE}`)

    // 使用统一的request实例发送请求
    const response = await request.post(`${config.apiPrefix}${API_ENDPOINTS.POS_STORE}`, {})

    console.log('门店数据获取成功:', response.body.storeInfoList)
    return response.body.storeInfoList

  } catch (error) {
    console.error('获取门店数据失败:', error)
    console.log('使用备用数据')

    // 返回模拟数据作为备用
    return [
      {
        "storeId": "5094",
        "storeName": " 长安霄边店",
        "pgSeq": "5",
        "pgName": "华南地区",
        "subId": "5_11",
        "subArea": "粤东区"
      },
      {
        "storeId": "5096",
        "storeName": "海源店",
        "pgSeq": "5",
        "pgName": "华南地区",
        "subId": "5_15",
        "subArea": "云贵区"
      },
      {
        "storeId": "1001",
        "storeName": "上海南京路店",
        "pgSeq": "1",
        "pgName": "华东地区",
        "subId": "1_01",
        "subArea": "上海区"
      },
      {
        "storeId": "1002",
        "storeName": "杭州西湖店",
        "pgSeq": "1",
        "pgName": "华东地区",
        "subId": "1_02",
        "subArea": "浙江区"
      }
    ]
  }
}