<template>
  <el-cascader
    v-model="selectedValue"
    :options="options"
    :props="cascaderProps"
    placeholder="请选择区域/城市/门店"
    clearable
    @change="handleChange"
    :loading="loading"
  />
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getPosStore } from "@/api";

const selectedValue = ref([]);
const options = ref([]);
const loading = ref(false);

const cascaderProps = {
  expandTrigger: "hover",
  checkStrictly: false,
};

const emit = defineEmits(["store-selected"]);

// 数据分组处理函数
const processStoreData = (storeList) => {
  // 首先按pgSeq分组
  const regionGroups = {};

  storeList.forEach((store) => {
    const { pgSeq, pgName, subId, subArea, storeId, storeName } = store;

    // 创建区域分组
    if (!regionGroups[pgSeq]) {
      regionGroups[pgSeq] = {
        value: pgSeq,
        label: pgName,
        children: {},
      };
    }

    // 在区域内按subId分组
    if (!regionGroups[pgSeq].children[subId]) {
      regionGroups[pgSeq].children[subId] = {
        value: subId,
        label: subArea,
        children: [],
      };
    }

    // 添加门店信息，格式为 "门店号-门店名称"
    regionGroups[pgSeq].children[subId].children.push({
      value: storeId,
      label: `${storeId}-${storeName.trim()}`,
      storeInfo: {
        storeId,
        storeName: storeName.trim(),
        pgSeq,
        pgName,
        subId,
        subArea,
      },
    });
  });

  // 转换为级联选择器需要的格式
  const result = Object.values(regionGroups).map((region) => ({
    ...region,
    children: Object.values(region.children),
  }));

  return result;
};

// 获取门店数据
const fetchStoreData = async () => {
  loading.value = true;
  try {
    const storeList = await getPosStore();
    options.value = processStoreData(storeList);
  } catch (error) {
    console.error("获取门店数据失败:", error);
    // 可以在这里添加错误提示
  } finally {
    loading.value = false;
  }
};

const handleChange = (value) => {
  if (value && value.length === 3) {
    // 查找选中的门店信息
    const selectedStore = findStoreInfo(value);

    emit("store-selected", {
      pgSeq: value[0],
      subId: value[1],
      storeId: value[2],
      storeInfo: selectedStore,
    });
  }
};

// 根据选择的值查找完整的门店信息
const findStoreInfo = (selectedValues) => {
  const [pgSeq, subId, storeId] = selectedValues;

  for (const region of options.value) {
    if (region.value === pgSeq) {
      for (const subArea of region.children) {
        if (subArea.value === subId) {
          for (const store of subArea.children) {
            if (store.value === storeId) {
              return store.storeInfo;
            }
          }
        }
      }
    }
  }
  return null;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchStoreData();
});
</script>

<style scoped>
.el-cascader {
  width: 300px;
}
</style>
