<template>
  <el-cascader
    v-model="selectedValue"
    :options="options"
    :props="cascaderProps"
    placeholder="请选择区域/城市/门店"
    clearable
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from "vue";

const selectedValue = ref([]);

const cascaderProps = {
  expandTrigger: "hover",
  checkStrictly: false,
};

const options = [
  {
    value: "east",
    label: "华东地区",
    children: [
      {
        value: "shanghai",
        label: "上海",
        children: [
          { value: "1001", label: "1001-闸北店" },
          { value: "1002", label: "1002-杨浦店" },
          { value: "1003", label: "1003-东环店111" },
        ],
      },
      {
        value: "hangzhou",
        label: "杭州",
        children: [
          { value: "2001", label: "西湖银泰店" },
          { value: "2002", label: "城西银泰店" },
        ],
      },
    ],
  },
  {
    value: "south",
    label: "华南地区",
    children: [
      {
        value: "guangzhou",
        label: "广州",
        children: [
          { value: "3001", label: "天河城店" },
          { value: "3002", label: "正佳广场店" },
        ],
      },
      {
        value: "shenzhen",
        label: "深圳",
        children: [
          { value: "4001", label: "华强北店" },
          { value: "4002", label: "海岸城店" },
        ],
      },
    ],
  },
];

const emit = defineEmits(["store-selected"]);

const handleChange = (value) => {
  if (value && value.length === 3) {
    emit("store-selected", {
      region: value[0],
      city: value[1],
      store: value[2],
    });
  }
};
</script>

<style scoped>
.el-cascader {
  width: 300px;
}
</style>
