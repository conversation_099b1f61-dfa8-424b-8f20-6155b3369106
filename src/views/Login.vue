<template>
  <div class="login-container">
    <!-- 背景动画 -->
    <div class="background-animation">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- 登录表单容器 -->
    <div class="login-wrapper">
      <div class="login-card">
        <!-- 标题 -->
        <div class="login-header">
          <h1 class="login-title">POS看盘中台</h1>
          <p class="login-subtitle">欢迎登录POS看盘管理系统</p>
        </div>

        <!-- 登录表单 -->
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          autocomplete="off"
          @submit.prevent="handleLogin"
        >
          <!-- 手机号输入 -->
          <el-form-item prop="phone">
            <el-input
              v-model="loginForm.phone"
              placeholder="请输入手机号"
              size="large"
              prefix-icon="Phone"
              maxlength="11"
              type="tel"
              autocomplete="off"
              @input="handlePhoneInput"
            />
          </el-form-item>

          <!-- 验证码输入 -->
          <el-form-item prop="code">
            <div class="code-input-wrapper">
              <el-input
                v-model="loginForm.code"
                placeholder="请输入验证码"
                size="large"
                prefix-icon="Message"
                maxlength="6"
                type="text"
                autocomplete="off"
                class="code-input"
              />
              <el-button
                :disabled="!canSendCode || countdown > 0"
                :loading="sendingCode"
                type="primary"
                size="large"
                class="send-code-btn"
                @click="handleSendCode"
              >
                {{ countdown > 0 ? `${countdown}s后重发` : "发送验证码" }}
              </el-button>
            </div>
          </el-form-item>

          <!-- 登录按钮 -->
          <el-form-item>
            <el-button
              :loading="logging"
              type="primary"
              size="large"
              class="login-btn"
              @click="handleLogin"
            >
              {{ logging ? "登录中..." : "登录" }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 开发环境提示 -->
        <div v-if="isDev" class="dev-tips">
          <el-alert
            title="开发环境提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>测试手机号: 13800138000</p>
              <p>验证码: 123456</p>
            </template>
          </el-alert>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/stores/user";

const router = useRouter();
const userStore = useUserStore();

// 表单数据
const loginForm = reactive({
  phone: "",
  code: "",
});

// 表单引用
const loginFormRef = ref();

// 状态
const logging = ref(false);
const sendingCode = ref(false);
const countdown = ref(0);
const countdownTimer = ref(null);

// 开发环境标识
const isDev = computed(() => process.env.NODE_ENV === "development");

// 是否可以发送验证码
const canSendCode = computed(() => {
  return /^1[3-9]\d{9}$/.test(loginForm.phone);
});

// 表单验证规则
const loginRules = {
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号",
      trigger: "blur",
    },
  ],
  code: [
    { required: true, message: "请输入验证码", trigger: "blur" },
    { pattern: /^\d{6}$/, message: "验证码为6位数字", trigger: "blur" },
  ],
};

// 处理手机号输入
const handlePhoneInput = (value) => {
  // 只允许输入数字
  loginForm.phone = value.replace(/\D/g, "");
};

// 发送验证码
const handleSendCode = async () => {
  if (!canSendCode.value) {
    ElMessage.warning("请输入正确的手机号");
    return;
  }

  sendingCode.value = true;

  try {
    const result = await userStore.sendVerificationCode(loginForm.phone);

    if (result.success) {
      ElMessage.success("验证码发送成功");

      // 开发环境自动填充验证码
      if (isDev.value && result.code) {
        loginForm.code = result.code;
        ElMessage.info(`开发环境自动填充验证码: ${result.code}`);
      }

      // 开始倒计时
      startCountdown();
    } else {
      ElMessage.error(result.message || "发送验证码失败");
    }
  } catch (error) {
    ElMessage.error("发送验证码失败");
  } finally {
    sendingCode.value = false;
  }
};

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value);
      countdownTimer.value = null;
    }
  }, 1000);
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    logging.value = true;

    const result = await userStore.login(loginForm.phone, loginForm.code);

    if (result.success) {
      ElMessage.success("登录成功");

      // 跳转到首页
      const redirect = router.currentRoute.value.query.redirect || "/";
      router.push(redirect);
    } else {
      ElMessage.error(result.message || "登录失败");
    }
  } catch (error) {
    ElMessage.error("登录失败");
  } finally {
    logging.value = false;
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
  }
});
</script>

<style lang="scss" scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 0%;
}

// 背景动画
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    left: 20%;
    animation-delay: 1s;
  }

  &.shape-3 {
    width: 60px;
    height: 60px;
    top: 30%;
    left: 60%;
    animation-delay: 2s;
  }

  &.shape-4 {
    width: 100px;
    height: 100px;
    top: 70%;
    left: 70%;
    animation-delay: 3s;
  }

  &.shape-5 {
    width: 140px;
    height: 140px;
    top: 10%;
    left: 40%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

// 登录表单
.login-wrapper {
  position: relative;
  z-index: 2;
  padding-right: 10%;
}

.login-card {
  width: 400px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.login-subtitle {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }
}

.code-input-wrapper {
  display: flex;
  gap: 12px;

  .code-input {
    flex: 1;
  }

  .send-code-btn {
    width: 120px;
    flex-shrink: 0;
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.dev-tips {
  margin-top: 24px;

  :deep(.el-alert__content) {
    p {
      margin: 4px 0;
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    justify-content: center;
    padding: 20px;
  }

  .login-card {
    width: 100%;
    max-width: 360px;
    padding: 32px 24px;
  }
}
</style>
