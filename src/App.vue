<template>
  <el-config-provider :locale="zhCn">
    <div class="app-container">
      <el-container>
        <el-aside width="220px">
          <el-menu
            router
            :default-active="$route.path"
            class="el-menu-vertical"
            background-color="#001529"
            text-color="#fff"
            active-text-color="#ffd04b"
          >
            <el-menu-item index="/">
              <el-icon><DataLine /></el-icon>
              <span>首页概览</span>
            </el-menu-item>
            <el-menu-item index="/pos-usage">
              <el-icon><PieChart /></el-icon>
              <span>POS使用率分析</span>
            </el-menu-item>
            <el-menu-item index="/order-analysis">
              <el-icon><Histogram /></el-icon>
              <span>订单量时段分析</span>
            </el-menu-item>
            <el-menu-item index="/device-status">
              <el-icon><Monitor /></el-icon>
              <span>设备状态监控</span>
            </el-menu-item>
            <el-menu-item index="/energy-optimization">
              <el-icon><Lightning /></el-icon>
              <span>能耗优化建议</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        <el-container>
          <el-header>
            <div class="header-title">
              <h2>POS看盘</h2>
            </div>
            <div class="header-user">
              <el-dropdown>
                <span class="el-dropdown-link">
                  管理员 <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="$router.push('/account')"
                      >个人中心</el-dropdown-item
                    >
                    <el-dropdown-item>设置</el-dropdown-item>
                    <el-dropdown-item>退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-header>
          <el-main>
            <router-view />
          </el-main>
          <el-footer>© 2023 POS能耗分析优化系统</el-footer>
        </el-container>
      </el-container>
    </div>
  </el-config-provider>
</template>

<script setup>
import { zhCn } from "element-plus/dist/locale/zh-cn.mjs";
</script>

<style lang="scss">
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

#app {
  height: 100vh;
}

.app-container {
  height: 100%;
}

.el-container {
  height: 100%;
}

.el-aside {
  background-color: #001529;
  color: #fff;
  .el-menu {
    border-right: none;
  }
}

.el-header {
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .header-user {
    margin-right: 20px;
    cursor: pointer;
  }
}

.el-footer {
  text-align: center;
  color: #999;
  line-height: 60px;
  background-color: #f5f7fa;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}

.el-menu-vertical {
  height: 100%;
}
</style>
