import axios from 'axios'
import config from '@/config/api'

// 创建axios实例
const request = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    console.log(`🚀 发送请求: ${config.method?.toUpperCase()} ${config.url}`)
    
    // 可以在这里添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    
    return config
  },
  (error) => {
    console.error('❌ 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log(`✅ 响应成功: ${response.config.url}`, response.data)
    
    // 统一处理响应数据
    const { data } = response
    
    // 根据业务需要处理响应
    if (data && data.rsCode === '00000000') {
      return data
    } else {
      console.warn('⚠️ 业务错误:', data)
      throw new Error(data?.msg || '请求失败')
    }
  },
  (error) => {
    console.error('❌ 响应错误:', error)
    
    // 统一错误处理
    let message = '网络错误'
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.message || `请求失败 (${status})`
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      if (error.code === 'ECONNABORTED') {
        message = '请求超时'
      } else {
        message = '网络连接失败'
      }
    }
    
    // 可以在这里添加全局错误提示
    // ElMessage.error(message)
    
    return Promise.reject(new Error(message))
  }
)

export default request
