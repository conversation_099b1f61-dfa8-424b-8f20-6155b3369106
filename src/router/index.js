import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  {
    path: '/pos-usage',
    name: 'PosUsage',
    component: () => import('@/views/PosUsage.vue')
  },
  {
    path: '/order-analysis',
    name: 'OrderAnalysis',
    component: () => import('@/views/OrderAnalysis.vue')
  },
  {
    path: '/device-status',
    name: 'DeviceStatus',
    component: () => import('@/views/DeviceStatus.vue')
  },
  {
    path: '/energy-optimization',
    name: 'EnergyOptimization',
    component: () => import('@/views/EnergyOptimization.vue')
  },
  {
    path: '/account',
    name: 'Account',
    component: () => import('@/views/Account.vue')
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router